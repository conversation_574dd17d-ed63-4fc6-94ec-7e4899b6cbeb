import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import YouTubeSearch from "@/components/youtube-search";

export default async function ProtectedPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/sign-in");
  }

  return (
    <div className="flex-1 w-full flex flex-col gap-8 py-8">
      <div className="max-w-4xl mx-auto w-full px-4">
        <h1 className="text-3xl font-bold mb-2">YouTuber Insights</h1>
        <p className="text-muted-foreground mb-8">
          Find the top-performing videos in your niche to inspire your content
          strategy.
        </p>

        <YouTubeSearch />
      </div>
    </div>
  );
}
