"use client";

import { useState, useEffect } from "react";
import { resetPasswordAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Label } from "@/components/ui/label";
import { useSearchParams } from "next/navigation";

export default function ResetPassword() {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordsMatch, setPasswordsMatch] = useState(true);
  const searchParams = useSearchParams();

  const success = searchParams.get("success");
  const error = searchParams.get("error");

  const message: Message | null = success
    ? { success }
    : error
      ? { error }
      : null;

  // Check if passwords match whenever either password field changes
  useEffect(() => {
    if (confirmPassword) {
      setPasswordsMatch(password === confirmPassword);
    } else {
      setPasswordsMatch(true);
    }
  }, [password, confirmPassword]);

  // Form is valid when both fields are filled and passwords match
  const isFormValid = password && confirmPassword && passwordsMatch;

  return (
    <form className="flex flex-col w-full max-w-md p-4 gap-2 [&>input]:mb-4">
      <h1 className="text-2xl font-medium">Reset password</h1>

      {success ? (
        <div className="py-4">
          <FormMessage message={message as Message} />
        </div>
      ) : (
        <>
          <p className="text-sm text-foreground/60">
            Please enter your new password below.
          </p>
          <Label htmlFor="password">New password</Label>
          <PasswordInput
            name="password"
            placeholder="New password"
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          <Label htmlFor="confirmPassword">Confirm password</Label>
          <PasswordInput
            name="confirmPassword"
            placeholder="Confirm password"
            required
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
          />
          {confirmPassword && !passwordsMatch && (
            <p className="text-sm text-destructive">Passwords do not match</p>
          )}
          <SubmitButton
            formAction={resetPasswordAction}
            disabled={!isFormValid}
          >
            Reset password
          </SubmitButton>
          {error && <FormMessage message={message as Message} />}
        </>
      )}
    </form>
  );
}
