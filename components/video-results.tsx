"use client";

import { useState } from "react";
import { YouTubeVideo, formatNumber, formatDate } from "@/utils/youtube-api";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
// Import Tabs components directly
import * as TabsPrimitive from "@radix-ui/react-tabs";
const Tabs = TabsPrimitive.Root;
const TabsList = TabsPrimitive.List;
const TabsTrigger = TabsPrimitive.Trigger;
const TabsContent = TabsPrimitive.Content;
import {
  Download,
  Eye,
  ThumbsUp,
  MessageSquare,
  Users,
  BarChart3,
  Loader2,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

interface VideoResultsProps {
  videos: YouTubeVideo[];
  isLoading: boolean;
  searchTerm: string;
}

export default function VideoResults({
  videos,
  isLoading,
  searchTerm,
}: VideoResultsProps) {
  const [exportFormat, setExportFormat] = useState<"txt" | "csv">("csv");

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Searching for top videos...</p>
      </div>
    );
  }

  if (videos.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-semibold mb-2">No videos found</h3>
        <p className="text-muted-foreground">
          Try adjusting your search term or filters to find videos.
        </p>
      </div>
    );
  }

  const handleExport = () => {
    if (exportFormat === "csv") {
      exportToCSV();
    } else {
      exportToTXT();
    }
  };

  const exportToCSV = () => {
    // CSV header
    let csv =
      "Title,Channel,Views,Likes,Comments,Subscribers,Engagement Rate,Published Date,URL\n";

    // Add each video as a row
    videos.forEach((video) => {
      const row = [
        `"${video.title.replace(/"/g, '""')}"`,
        `"${video.channelTitle.replace(/"/g, '""')}"`,
        video.viewCount,
        video.likeCount,
        video.commentCount,
        video.subscriberCount || 0,
        `"${video.engagementRate ? video.engagementRate.toFixed(2) + "%" : "0%"}"`,
        `"${formatDate(video.publishedAt)}"`,
        `"https://www.youtube.com/watch?v=${video.id}"`,
      ];

      csv += row.join(",") + "\n";
    });

    // Create and download the file
    downloadFile(
      csv,
      `youtube-insights-${searchTerm.replace(/\s+/g, "-")}.csv`,
      "text/csv"
    );
  };

  const exportToTXT = () => {
    // Create text content
    let text = `YouTube Insights - Top Videos for "${searchTerm}"\n`;
    text += `Generated on ${new Date().toLocaleDateString()}\n\n`;

    // Add each video
    videos.forEach((video, index) => {
      text += `${index + 1}. ${video.title}\n`;
      text += `   Channel: ${video.channelTitle}\n`;
      text += `   Views: ${video.viewCount}\n`;
      text += `   Likes: ${video.likeCount}\n`;
      text += `   Comments: ${video.commentCount}\n`;
      text += `   Subscribers: ${video.subscriberCount || "N/A"}\n`;
      text += `   Engagement Rate: ${video.engagementRate ? video.engagementRate.toFixed(2) + "%" : "N/A"}\n`;
      text += `   Published: ${formatDate(video.publishedAt)}\n`;
      text += `   URL: https://www.youtube.com/watch?v=${video.id}\n\n`;
    });

    // Create and download the file
    downloadFile(
      text,
      `youtube-insights-${searchTerm.replace(/\s+/g, "-")}.txt`,
      "text/plain"
    );
  };

  const downloadFile = (
    content: string,
    fileName: string,
    contentType: string
  ) => {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Top Videos for "{searchTerm}"</h2>
          <p className="text-muted-foreground">{videos.length} videos found</p>
        </div>

        <div className="flex items-center gap-2">
          <Tabs
            defaultValue="csv"
            onValueChange={(v) => setExportFormat(v as "txt" | "csv")}
          >
            <TabsList className="grid h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground w-[180px] grid-cols-2">
              <TabsTrigger
                value="csv"
                className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
              >
                CSV
              </TabsTrigger>
              <TabsTrigger
                value="txt"
                className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
              >
                TXT
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Button onClick={handleExport} className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {videos.map((video) => (
          <Card key={video.id} className="overflow-hidden">
            <CardContent className="p-0">
              <div className="flex flex-col md:flex-row">
                <div className="relative w-full md:w-64 h-48 flex-shrink-0">
                  <Image
                    src={video.thumbnailUrl}
                    alt={video.title}
                    fill
                    className="object-cover"
                  />
                </div>

                <div className="p-4 flex-1">
                  <div className="flex flex-col h-full justify-between">
                    <div>
                      <Link
                        href={`https://www.youtube.com/watch?v=${video.id}`}
                        target="_blank"
                        className="text-lg font-semibold hover:text-primary transition-colors line-clamp-2"
                      >
                        {video.title}
                      </Link>

                      <Link
                        href={`https://www.youtube.com/channel/${video.channelId}`}
                        target="_blank"
                        className="text-sm text-muted-foreground hover:text-primary transition-colors mt-1 block"
                      >
                        {video.channelTitle}
                      </Link>

                      <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                        {video.description}
                      </p>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mt-4">
                      <div className="flex items-center gap-2">
                        <Eye className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          {formatNumber(video.viewCount)}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <ThumbsUp className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          {formatNumber(video.likeCount)}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <MessageSquare className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          {formatNumber(video.commentCount)}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          {video.subscriberCount
                            ? formatNumber(video.subscriberCount)
                            : "N/A"}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <BarChart3 className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          {video.engagementRate
                            ? video.engagementRate.toFixed(2) + "%"
                            : "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
