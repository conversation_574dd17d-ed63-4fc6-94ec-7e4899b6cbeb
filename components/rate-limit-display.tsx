"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Crown, Zap, Rocket } from "lucide-react";
import Link from "next/link";

interface RateLimitInfo {
  tier: "free" | "pro" | "business";
  limit: number;
  used: number;
  remaining: number;
}

interface RateLimitDisplayProps {
  className?: string;
}

const tierConfig = {
  free: {
    name: "Free",
    icon: Zap,
    color: "bg-gray-500",
    badgeVariant: "secondary" as const,
  },
  pro: {
    name: "Pro",
    icon: Crown,
    color: "bg-blue-500",
    badgeVariant: "default" as const,
  },
  business: {
    name: "Business",
    icon: Rocket,
    color: "bg-purple-500",
    badgeVariant: "default" as const,
  },
};

export default function RateLimitDisplay({ className }: RateLimitDisplayProps) {
  const [rateLimitInfo, setRateLimitInfo] = useState<RateLimitInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRateLimitInfo();
  }, []);

  const fetchRateLimitInfo = async () => {
    try {
      const response = await fetch("/api/rate-limit/info");
      if (response.ok) {
        const data = await response.json();
        setRateLimitInfo(data);
      }
    } catch (error) {
      console.error("Error fetching rate limit info:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="animate-pulse">
            <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
            <div className="h-2 bg-muted rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!rateLimitInfo) {
    return null;
  }

  const { tier, limit, used, remaining } = rateLimitInfo;
  const config = tierConfig[tier];
  const Icon = config.icon;
  const usagePercentage = (used / limit) * 100;
  const isNearLimit = usagePercentage > 80;
  const isAtLimit = remaining === 0;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Usage Today</CardTitle>
          <Badge variant={config.badgeVariant} className="flex items-center gap-1">
            <Icon className="h-3 w-3" />
            {config.name}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Searches</span>
            <span className="font-medium">
              {used} / {limit === 10000 ? "∞" : limit}
            </span>
          </div>
          
          {limit !== 10000 && (
            <Progress 
              value={usagePercentage} 
              className="h-2"
            />
          )}
          
          <div className="text-xs text-muted-foreground">
            {tier === "business" ? (
              "Unlimited searches"
            ) : isAtLimit ? (
              <span className="text-destructive font-medium">
                Daily limit reached
              </span>
            ) : isNearLimit ? (
              <span className="text-orange-600 font-medium">
                {remaining} searches remaining
              </span>
            ) : (
              `${remaining} searches remaining today`
            )}
          </div>
          
          {(isAtLimit || (isNearLimit && tier === "free")) && (
            <div className="pt-2 border-t">
              <Link href="/pricing">
                <Button size="sm" className="w-full">
                  {tier === "free" ? "Upgrade to Pro" : "Upgrade to Business"}
                </Button>
              </Link>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
