import { Ratelimit } from "@upstash/ratelimit";
import { Redis } from "@upstash/redis";

// Create Redis instance
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

// Define rate limits for different user tiers
export const rateLimits = {
  free: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(5, "1 d"), // 5 requests per day
    analytics: true,
    prefix: "ratelimit:free",
  }),
  pro: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(100, "1 d"), // 100 requests per day
    analytics: true,
    prefix: "ratelimit:pro",
  }),
  business: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(10000, "1 d"), // Effectively unlimited (10k per day)
    analytics: true,
    prefix: "ratelimit:business",
  }),
};

// User tier type
export type UserTier = "free" | "pro" | "business";

// Function to get user's subscription tier
export async function getUserTier(userId: string): Promise<UserTier> {
  try {
    // For now, we'll default to 'free' tier
    // Later, this will check the user's subscription status from Stripe/database
    const userTierData = await redis.get(`user:${userId}:tier`);
    
    if (userTierData && typeof userTierData === 'string') {
      return userTierData as UserTier;
    }
    
    return "free"; // Default to free tier
  } catch (error) {
    console.error("Error getting user tier:", error);
    return "free"; // Default to free tier on error
  }
}

// Function to set user tier (for when they subscribe/upgrade)
export async function setUserTier(userId: string, tier: UserTier): Promise<void> {
  try {
    await redis.set(`user:${userId}:tier`, tier);
  } catch (error) {
    console.error("Error setting user tier:", error);
  }
}

// Function to check rate limit for a user
export async function checkRateLimit(userId: string, userTier?: UserTier) {
  try {
    // Get user tier if not provided
    const tier = userTier || await getUserTier(userId);
    
    // Get the appropriate rate limiter
    const rateLimit = rateLimits[tier];
    
    // Check the rate limit
    const result = await rateLimit.limit(userId);
    
    return {
      success: result.success,
      limit: result.limit,
      remaining: result.remaining,
      reset: result.reset,
      tier,
    };
  } catch (error) {
    console.error("Rate limit check error:", error);
    // On error, allow the request but log it
    return {
      success: true,
      limit: 0,
      remaining: 0,
      reset: new Date(),
      tier: "free" as UserTier,
    };
  }
}

// Function to get rate limit info without consuming a request
export async function getRateLimitInfo(userId: string, userTier?: UserTier) {
  try {
    const tier = userTier || await getUserTier(userId);
    const rateLimit = rateLimits[tier];
    
    // Get current usage without incrementing
    const key = `${rateLimit.prefix}:${userId}`;
    const current = await redis.get(key) || 0;
    
    const limits = {
      free: 5,
      pro: 100,
      business: 10000,
    };
    
    return {
      tier,
      limit: limits[tier],
      used: typeof current === 'number' ? current : 0,
      remaining: limits[tier] - (typeof current === 'number' ? current : 0),
    };
  } catch (error) {
    console.error("Error getting rate limit info:", error);
    return {
      tier: "free" as UserTier,
      limit: 5,
      used: 0,
      remaining: 5,
    };
  }
}
