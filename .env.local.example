# Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Google OAuth configuration
# You can get these from the Google Cloud Console (https://console.cloud.google.com/)
# 1. Create a new project
# 2. Go to "APIs & Services" > "Credentials"
# 3. Create an OAuth client ID (Web application)
# 4. Add authorized redirect URIs:
#    - http://localhost:3000/auth/callback (for local development)
#    - https://your-production-domain.com/auth/callback (for production)

# YouTube Data API v3
# Get your API key from Google Cloud Console:
# 1. Go to https://console.cloud.google.com/
# 2. Create a project (or use an existing one)
# 3. Enable the YouTube Data API v3
# 4. Create an API key in the Credentials section
NEXT_PUBLIC_YOUTUBE_API_KEY=your-youtube-api-key
